import { omit, orderBy } from 'lodash-es';
import TerraFilterActions from '~/naavix/components/action-elements/terra/terra-filters-actions.vue';
import TerraToggleLayers from '~/naavix/components/action-elements/terra/terra-toggle-layers.vue';
import TerraUpdateClasses from '~/naavix/components/action-elements/terra/terra-update-classes.vue';
import { useTerraStore } from '~/terra/store/terra.store';
import { useTerraHelperComposable } from '~/terra/utils/helper-composable.js';

export function useTerraActionHelpers() {
  const terra_store = useTerraStore();

  const { getProjectOptions } = useTerraHelperComposable();

  const projects_list = computed(() => getProjectOptions());

  const features_on_map = computed(() => {
    return terra_store.features_on_map.map(feature => ({
      uid: feature.properties.uid,
      name: feature.properties?.name || 'Unnamed',
      type: terra_store.feature_types?.[feature?.properties?.featureTypeId]?.name || 'No Type',
    }));
  });

  function getFilteredFeatures(search_regex) {
    const regex = new RegExp(search_regex);
    const search_vectors = features_on_map.value.filter((feature) => {
      return regex.test(feature.name || '');
    });
    return search_vectors;
  }

  function getTerraActionsMap() {
    return {
      get_all_layers: {
        type: 'implicit',
        // action is needed only for implicit actions. For explicit actions, the action is defined in the component.
        action: (function_call, callback_fn) => {
          const all_layers_data = projects_list.value.reduce((acc, layer) => {
            acc.push({
              layer: layer.name,
              sublayers: layer.projects.map(sub => sub.name),
            });
            return acc;
          }, []);
          logger.log('all_layers_data:', all_layers_data);
          const data = {
            response: all_layers_data,
          };
          callback_fn(data);
        },
      },
      toggle_layers: {
        type: 'explicit',
        // component is needed only for explicit actions.
        component: TerraToggleLayers,
      },
      get_visible_layers: {
        type: 'implicit',
        action: (function_call, callback_fn) => {
          const visible_layers_data = projects_list.value.reduce((acc, layer) => {
            const is_layer_visible = layer.projects.some(sub => terra_store.active_projects_map?.[sub.uid]?.features_enabled);
            if (is_layer_visible) {
              acc.push({
                layer: layer.name,
                sublayers: layer.projects.reduce((sub_layers, sub) => {
                  if (terra_store.active_projects_map?.[sub.uid]?.features_enabled) {
                    sub_layers.push(sub.name);
                  };
                  return sub_layers;
                }, []),
              });
            }
            return acc;
          }, []);
          logger.log('visible_layers_data:', visible_layers_data);
          const data = {
            response: visible_layers_data,
          };
          callback_fn(data);
        },
      },
      get_classes_breakdown: {
        type: 'implicit',
        action: (function_call, callback_fn) => {
          const classes_breakdown_data = terra_store.ftg.reduce((acc, class_group) => {
            const is_class_group_visible = class_group.featureTypes?.some(cls => terra_store.features_count[cls.id] > 0);
            if (class_group?.featureTypes?.length && is_class_group_visible) {
              acc.push({
                class_group: class_group.name,
                classes: class_group.featureTypes.map(cls => ({
                  name: cls.name,
                  count: terra_store.features_count[cls.id] || 0,
                })),
              });
            }
            return acc;
          }, []);
          logger.log('classes_breakdown_data:', classes_breakdown_data);
          const data = {
            response: classes_breakdown_data,
          };
          callback_fn(data);
        },
      },
      get_filters: {
        type: 'implicit_component',
        component: TerraFilterActions,
      },
      set_filters: {
        type: 'implicit_component',
        component: TerraFilterActions,
      },
      get_sample_vectors: {
        type: 'implicit',
        action: (function_call, callback_fn) => {
          const features_samples_data = features_on_map.value.slice(0, 25).map(f => omit(f, ['uid']));
          logger.log('sample_vectors_data:', features_samples_data, function_call);
          const data = {
            response: features_samples_data,
          };
          callback_fn(data);
        },
      },
      search_vectors: {
        type: 'implicit',
        action: (function_call, callback_fn) => {
          const search_regex = function_call.arguments.regex || '';
          const search_vectors = getFilteredFeatures(search_regex).slice(0, 25).map(f => omit(f, ['uid']));
          logger.log('search_vectors_data:', search_vectors, function_call);
          const data = {
            response: search_vectors,
          };
          callback_fn(data);
        },
      },
      select_vectors: {
        type: 'implicit',
        action: (function_call, callback_fn) => {
          const search_regex = function_call.arguments.regex || '';
          const selected_vectors = getFilteredFeatures(search_regex);
          logger.log('select_vectors:', selected_vectors, function_call);
          terra_store.selected_features = selected_vectors.map(f => terra_store.features_hash[f.uid]);
          callback_fn({
            response: 'Successfully selected vectors',
          });
        },
      },
      update_classes: {
        type: 'explicit',
        component: TerraUpdateClasses,
      },
    };
  }

  function getTerraInputSuggestions() {
    const layers_suggestions = Object.values(terra_store.container.groups || {}).map((group) => {
      const group_name = group.name;
      const children = group.projects
        ? Object.values(group.projects).map((project) => {
          return { label: project.name };
        })
        : [];
      return {
        label: group_name,
        children,
      };
    });
    const workflows_suggestions = orderBy(Object.values(terra_store.terra_workflows), wf => wf?.name?.toLowerCase(), ['asc']).map(wf => ({
      label: wf.name,
      children: Object.values(terra_store.workflow_fields_hash({ workflow: wf.uid })?.fields).map((field) => { return { label: field.name }; }),
    }));
    return [...layers_suggestions, ...workflows_suggestions];
  }

  return {
    getTerraActionsMap,
    getTerraInputSuggestions,
  };
}
