<script setup>
import { vOnClickOutside } from '@vueuse/components';
import DOMPurify from 'dompurify';
import Fuse from 'fuse.js';
import { cloneDeep, orderBy } from 'lodash-es';
import { marked } from 'marked';
import { nanoid } from 'nanoid';
import { storeToRefs } from 'pinia';
import { nextTick, onMounted, onUnmounted, watch } from 'vue';
import useAbortController from '~/common/composables/abort-controller.js';
import { useCommonImports } from '~/common/composables/common-imports.composable.js';
import { sleep } from '~/common/utils/common.utils';
import { load_js_css_file } from '~/common/utils/load-script.util';
import { useExampleModuleActionHelpers } from '~/naavix/components/action-elements/example-module/example-module-actions.composable.js';
import { useTerraActionHelpers } from '~/naavix/components/action-elements/terra/terra-actions.composable';
import NaavixGraph from '~/naavix/components/resource-elements/naavix-graph.vue';
import NaavixPmActivities from '~/naavix/components/resource-elements/naavix-pm-activities.vue';
import { useChatScroll } from '~/naavix/composables/naavix-helpers.composable';
import { useNaavixStore } from '~/naavix/store/naavix.store';
import { useTerraStore } from '~/terra/store/terra.store';

const props = defineProps({
  variant: {
    type: String,
    default: 'mini',
    validator(value) {
      return ['mini', 'full'].includes(value);
    },
  },
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  starterSuggestions: {
    type: Array,
    default: () => ([]),
  },
});

const emit = defineEmits('close');

const { $services, $t, $toast } = useCommonImports();

const naavix_store = useNaavixStore();
const terra_store = useTerraStore();
const { set_chats } = naavix_store;
const { active_chat_id, are_messages_loading, messages, all_chats, is_explicit_function_call_active, get_naavix_agent } = storeToRefs(naavix_store);

const { getExampleModuleActionsMap } = useExampleModuleActionHelpers();
const { getTerraActionsMap, getTerraInputSuggestions } = useTerraActionHelpers();
const controllers = useAbortController();

const {
  isAtBottom,
  chatContainer,
  scrollToBottom,
  setupScrollListeners,
  cleanupScrollListeners,
} = useChatScroll();

const agents_list = [
  {
    name: 'general',
    label: 'General',
  },
  {
    name: 'forms',
    label: get_naavix_agent.value.form_name,
  },
  {
    name: 'pm',
    label: 'Project management',
  },
  {
    name: 'terra',
    label: 'Construction monitoring',
  },
];

const render_map = {
  chart: NaavixGraph,
  activities: NaavixPmActivities,
};

const actions_map = {
  ...getExampleModuleActionsMap(),
  ...getTerraActionsMap(),
};

// const follow_up_question_suggestions = [
//   'Do you want to monitor specific safety metrics for the upcoming week?',
//   'Need help drafting safety guidelines or preparing for a safety audit or something else?',
//   'Would you like to track the safety measures for future projects as well?',
// ];

const state = reactive({
  user_input: '',
  is_streaming: false,
  current_agent: cloneDeep(agents_list.find(agent => agent.name === get_naavix_agent.value.module)),
  deep_dive: false,
  usage_quota_reached: '',
  show_popup: false,
  popup_position: {
    x: 0,
    y: 0,
  },
  popup_direction: 'top',
  suggestion_index: 0,
  fuse: null,
});

const inputContainer = ref(null);
const textarea = ref(null);

const input_suggestion_items = computed(() => {
  const atIdx = state.user_input.lastIndexOf('@');
  if (atIdx === -1)
    return [];
  const cursorPos = textarea.value ? textarea.value.selectionStart : state.user_input.length;
  if (cursorPos <= atIdx + 1)
    return [];
  const searchText = state.user_input.substring(atIdx + 1, cursorPos).trim();
  if (searchText === '')
    return [];
  return state.fuse.search(searchText).map(result => result.item);
});

const function_calls_map = computed(() => {
  return messages.value.reduce((acc, message) => {
    message.function_calls?.forEach?.((function_call) => {
      acc[function_call.id] = function_call;
    });
    return acc;
  }, {});
});

function renderMarkdown(text) {
  const sanitizedHtml = DOMPurify.sanitize(marked.parse(text));
  return sanitizedHtml;
}

async function streamText(text) {
  const words = text.split(' ');

  for (let i = 0; i < words.length; i++) {
    const words_to_add = words[i] + (i < words.length - 1 ? ' ' : '');
    messages.value[messages.value.length - 1].message += words_to_add;
    scrollToBottom();
  }
}

async function handleFunctionCall(chunk) {
  if (!chunk.content.function_call.title)
    chunk.content.function_call.title = 'N/A';
  if (!chunk.content.function_call.description)
    chunk.content.function_call.description = 'N/A';
  messages.value[messages.value.length - 1].message = '';
  messages.value[messages.value.length - 1].function_calls.push(chunk.content.function_call);
  messages.value[messages.value.length - 1].current_function_call = chunk.content.function_call;
}

function handleFunctionResponse(chunk) {
  messages.value[messages.value.length - 1].function_responses.push(chunk.content.function_response);
}

async function sendMessage(retry = false) {
  if (state.usage_quota_reached) {
    if (retry)
      state.user_input = state.usage_quota_reached;
    state.usage_quota_reached = '';
  }
  await nextTick();

  if (
    !state.user_input.trim()
    || are_messages_loading.value
    || state.is_streaming
  ) {
    return;
  }

  const messageText = state.user_input;
  state.user_input = '';
  textarea.value.style.height = '48px';

  // Add user message
  messages.value.push({
    id: nanoid(),
    sender: 'user',
    message: messageText,
  });

  isAtBottom.value = true;
  scrollToBottom();

  // Add assistant message placeholder
  const naavix_message_id = nanoid();
  messages.value.push({
    id: naavix_message_id,
    sender: 'assistant',
    function_calls: [],
    current_function_call: null,
    message: '',
    function_responses: [],
    is_streaming: true,
  });

  const body = {
    message: messageText,
    resource_id: get_naavix_agent.value.resource_id,
    resource_type: get_naavix_agent.value.resource_type,
  };
  await streamMessage(naavix_message_id, body);
}

async function streamMessage(naavix_message_id, body) {
  try {
    state.is_streaming = true;
    const signal = controllers.add('stream_naavix_response');

    const response = await $services.ai.stream({
      agent_name: get_naavix_agent.value.module,
      session_uuid: active_chat_id.value,
      query: {
        ...(state.deep_dive && { mode: 'deep_dive' }),
      },
      body,
      signal,
    });

    if (response.status !== 200)
      throw new Error(`HTTP error! status: ${response.status}`);

    const reader = response.data.getReader();
    const decoder = new TextDecoder();

    while (true) {
      const { value, done } = await reader.read();
      if (done)
        break;

      const chunk = decoder.decode(value);
      const lines = chunk.split('\n').filter(line => line.trim());

      for (const line of lines) {
        if (!line.startsWith('data: '))
          continue;

        const data = JSON.parse(line.slice(6));

        if (!messages.value.find(message => message.id === naavix_message_id)) {
          controllers.abort('stream_naavix_response');
          $toast({
            title: 'Conversation stopped',
            text: 'Previous response was interrupted. You can now start a new conversation.',
            type: 'warning',
          });
          break;
        }

        if (data.event === 'partial_message') {
          messages.value[messages.value.length - 1].current_function_call = null;
          await streamText(data.content);
        }
        else if (data.event === 'message') {
          messages.value[messages.value.length - 1].message = data.content;
          messages.value[messages.value.length - 1].current_function_call = null;
        }
        else if (data.event === 'function_call') {
          await handleFunctionCall(data);
        }
        else if (data.event === 'function_response') {
          handleFunctionResponse(data);
        }
        else if (data.event === 'error') {
          throw data;
        }
      }
    }

    if (!all_chats.value.find(chat => chat.id === active_chat_id.value))
      await set_chats();
  }
  catch (error) {
    if (error.name === 'AbortError') {
      return;
    }

    if (error.type === 'rate_limit') {
      state.usage_quota_reached = body.message;
    }
    else {
      logger.error('Error', error);
      $toast({
        title: 'Something went wrong',
        text: 'Please try again',
        type: 'error',
      });
    }
  }
  finally {
    state.is_streaming = false;
    messages.value[messages.value.length - 1].is_streaming = false;
    await nextTick();
    if (isAtBottom.value) {
      await sleep(100);
      scrollToBottom();
    }
  }
}

function stopStreaming() {
  state.is_streaming = false;
  messages.value[messages.value.length - 1].current_function_call = null;
  messages.value[messages.value.length - 1].is_streaming = false;
  controllers.abort('stream_naavix_response');
}

function handleEnterKeyPress(event) {
  if (event.shiftKey) {
    return;
  }
  if (state.show_popup) {
    event.preventDefault();
    return;
  }
  event.preventDefault();
  sendMessage();
}

function adjustHeight(element) {
  element.style.height = '48px';
  const new_height = Math.min(element.scrollHeight, 160);
  element.style.height = `${new_height}px`;
}

async function handleSuggestionClick(suggestion) {
  state.user_input = suggestion;
  await nextTick();
  adjustHeight(textarea.value);
}

function onImplicitActionComplete(data) {
  onActionContinue(data);
}

function onImplicitAction(action) {
  if (action.action) {
    action.action(messages.value[messages.value.length - 1].current_function_call, onImplicitActionComplete);
  }
}

function onActionContinue(response) {
  const current_function_call = messages.value[messages.value.length - 1].current_function_call;
  const body = {
    type: 'function_response',
    id: current_function_call.id,
    name: current_function_call.name,
    response: JSON.stringify(response.response),
    resource_id: get_naavix_agent.value.resource_id,
    resource_type: get_naavix_agent.value.resource_type,
    message: '',
  };
  handleFunctionResponse({
    content: {
      function_response: {
        id: current_function_call.id,
        name: current_function_call.name,
        response: {
          artifact_id: '',
          artifact_type: '',
          content: response.response,
          status: 'success',
        },
      },
    },
    event: 'function_response',
    is_final_response: false,
    role: 'model',
  });
  streamMessage(messages.value[messages.value.length - 1].id, body);
}

function onActionCancel() {
  messages.value[messages.value.length - 1].current_function_call = null;
}

// function exportMessageAsReport(_message) {
//   $toast({
//     title: 'A detailed report will be sent to your email shortly',
//     text: 'Coming soon™',
//     type: 'info',
//   });
// }

function calculatePopupPositionAtChar(textareaEl, atIdx) {
  const PADDING = 5;
  const POPUP_HEIGHT = 280;
  const POPUP_WIDTH = 250;
  const VERTICAL_OFFSET = 20;
  const windowHeight = window.innerHeight;
  const windowWidth = window.innerWidth;

  const mirrorDiv = document.createElement('div');
  const style = window.getComputedStyle(textareaEl);
  for (const prop of [
    'boxSizing',
    'width',
    'height',
    'fontSize',
    'fontFamily',
    'fontWeight',
    'fontStyle',
    'letterSpacing',
    'textTransform',
    'wordSpacing',
    'textIndent',
    'whiteSpace',
    'lineHeight',
    'paddingTop',
    'paddingRight',
    'paddingBottom',
    'paddingLeft',
    'borderTopWidth',
    'borderRightWidth',
    'borderBottomWidth',
    'borderLeftWidth',
    'overflow',
    'direction',
    'textAlign',
    'backgroundColor',
    'color',
  ]) {
    mirrorDiv.style[prop] = style[prop];
  }
  mirrorDiv.style.position = 'absolute';
  mirrorDiv.style.visibility = 'hidden';
  mirrorDiv.style.whiteSpace = 'pre-wrap';
  mirrorDiv.style.wordWrap = 'break-word';
  mirrorDiv.style.left = '-9999px';
  mirrorDiv.style.top = '0';
  mirrorDiv.style.zIndex = '-1';

  const value = textareaEl.value;
  const beforeAt = value.substring(0, atIdx);
  mirrorDiv.textContent = beforeAt;

  const marker = document.createElement('span');
  marker.textContent = '@';
  mirrorDiv.appendChild(marker);

  document.body.appendChild(mirrorDiv);

  const markerRect = marker.getBoundingClientRect();
  const textareaRect = textareaEl.getBoundingClientRect();
  let x = textareaRect.left + markerRect.left - mirrorDiv.getBoundingClientRect().left;
  let y = textareaRect.top + markerRect.top - mirrorDiv.getBoundingClientRect().top + marker.offsetHeight;

  document.body.removeChild(mirrorDiv);

  let direction = 'bottom';
  if (y + POPUP_HEIGHT + VERTICAL_OFFSET > windowHeight && textareaRect.top - POPUP_HEIGHT - VERTICAL_OFFSET > 0) {
    direction = 'top';
    y = y - POPUP_HEIGHT - marker.offsetHeight - PADDING - VERTICAL_OFFSET;
  }
  else {
    y = y + VERTICAL_OFFSET;
  }
  if (x + POPUP_WIDTH > windowWidth) {
    x = windowWidth - POPUP_WIDTH - PADDING;
  }
  if (x < 0)
    x = PADDING;
  if (y < 0)
    y = PADDING;

  return { x, y, direction };
}

function handleInput(event) {
  adjustHeight(event.target);

  const text = event.target.value;
  const cursorPosition = event.target.selectionStart;
  const atIdx = text.lastIndexOf('@', cursorPosition - 1);
  if (atIdx !== -1 && cursorPosition > atIdx + 1) {
    const searchText = text.substring(atIdx + 1, cursorPosition).trim();
    if (searchText.length > 0) {
      const suggestions = state.fuse.search(searchText).map(result => result.item);
      if (suggestions.length === 0) {
        state.show_popup = false;
        return;
      }
      const position = calculatePopupPositionAtChar(event.target, atIdx);
      state.popup_position = {
        x: position.x,
        y: position.y,
      };
      state.popup_direction = position.direction;
      state.show_popup = true;
      state.suggestion_index = 0;
      return;
    }
  }
  state.show_popup = false;
}

function closePopup() {
  state.show_popup = false;
}

function scrollSelectedItemIntoView() {
  if (!state.show_popup)
    return;

  const items = input_suggestion_items.value;
  const flatItems = [];
  items.forEach((item) => {
    flatItems.push({ item, child: null });
    if (item.children) {
      item.children.forEach((child) => {
        flatItems.push({ item, child });
      });
    }
  });

  const selectedItem = flatItems[state.suggestion_index];
  if (!selectedItem)
    return;

  const refKey = selectedItem.child
    ? `${selectedItem.item.label}-${selectedItem.child.label}`
    : selectedItem.item.label;

  nextTick(() => {
    const element = document.querySelector(`[data-suggestion-ref="${refKey}"]`);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'nearest',
      });
    }
  });
}

function insertMention(item, child = null) {
  const textareaEl = document.querySelector('textarea');
  const cursorPos = textareaEl.selectionStart;
  const atIdx = state.user_input.lastIndexOf('@', cursorPos - 1);
  let label = item.label;
  if (child) {
    label = `${item.label} > ${child.label}`;
  }
  const beforeAt = state.user_input.substring(0, atIdx);
  const afterSearch = state.user_input.substring(cursorPos);
  const newText = `${beforeAt + label} ${afterSearch}`;
  state.user_input = newText;
  state.show_popup = false;
  nextTick(() => {
    textareaEl.focus();
    const newCursorPos = beforeAt.length + label.length + 1;
    textareaEl.setSelectionRange(newCursorPos, newCursorPos);
  });
}
function handleSuggestionKeydown(event) {
  if (!state.show_popup)
    return;
  const items = input_suggestion_items.value;
  let totalItems = items.length;
  const flatItems = [];
  items.forEach((item) => {
    flatItems.push({ item, child: null });
    if (item.children) {
      item.children.forEach((child) => {
        flatItems.push({ item, child });
      });
    }
  });
  totalItems = flatItems.length;

  if (event.key === 'ArrowDown') {
    event.preventDefault();
    state.suggestion_index = (state.suggestion_index + 1) % totalItems;
    nextTick(() => scrollSelectedItemIntoView());
  }
  else if (event.key === 'ArrowUp') {
    event.preventDefault();
    state.suggestion_index = (state.suggestion_index - 1 + totalItems) % totalItems;
    nextTick(() => scrollSelectedItemIntoView());
  }
  else if (event.key === 'Enter') {
    event.preventDefault();
    const selected = flatItems[state.suggestion_index];
    insertMention(selected.item, selected.child);
  }
  else if (event.key === 'Escape') {
    state.show_popup = false;
  }
}

function getFlatIndex(itemIdx, subIdx) {
  let idx = 0;
  for (let i = 0; i < input_suggestion_items.value.length; i++) {
    if (i === itemIdx && subIdx === null)
      return idx;
    idx++;
    if (input_suggestion_items.value[i].children) {
      for (let j = 0; j < input_suggestion_items.value[i].children.length; j++) {
        if (i === itemIdx && j === subIdx)
          return idx;
        idx++;
      }
    }
  }
  return -1;
}

watch(() => messages.value[messages.value.length - 1]?.current_function_call, () => {
  if (actions_map[messages.value[messages.value.length - 1]?.current_function_call?.name]?.type === 'implicit') {
    onImplicitAction(actions_map[messages.value[messages.value.length - 1].current_function_call.name]);
  }
}, { deep: true });

watch(() => [messages.value], () => {
  scrollToBottom();
}, { deep: true });

watch(() => are_messages_loading.value, () => {
  if (!are_messages_loading.value) {
    isAtBottom.value = true;
    scrollToBottom();
  }
});

watch(() => active_chat_id.value, async () => {
  state.user_input = '';
  cleanupScrollListeners();
  await nextTick();
  setupScrollListeners();
});

watch(() => actions_map?.[messages.value?.[messages.value.length - 1]?.current_function_call?.name]
  && actions_map[messages.value[messages.value.length - 1].current_function_call.name].type === 'explicit'
  && !state.is_streaming, (value) => {
  is_explicit_function_call_active.value = !!value;
});

onMounted(() => {
  if (messages.value.length)
    scrollToBottom();
  setupScrollListeners();
  load_js_css_file('https://cdn.plot.ly/plotly-cartesian-3.0.1.js', 'plotly', 'js');
  let input_suggestions = [];
  if (get_naavix_agent.value?.module === 'terra') {
    input_suggestions = getTerraInputSuggestions();
  }
  state.fuse = new Fuse(input_suggestions, {
    keys: ['label', 'children.label'],
  });
});

onUnmounted(() => {
  cleanupScrollListeners();
});
</script>

<template>
  <div :key="active_chat_id" class="flex flex-col h-full">
    <div ref="chatContainer" class="flex-1 scrollbar overscroll-contain py-3">
      <div v-if="are_messages_loading" class="w-full px-4 mt-[200px]">
        <HawkLoader container_class="m-0" />
        <div class="text-sm font-normal text-gray-500 text-center mt-2">
          {{ $t('Loading messages') }}...
        </div>
      </div>
      <template v-else-if="messages.length === 0">
        <div class="min-h-full px-4 flex flex-col gap-3 justify-center items-center">
          <div class="min-h-10 w-10 flex items-center justify-center rounded-full bubble mb-1">
            <IconHawkStarSix class="text-white" />
          </div>
          <div class="text-lg font-semibold text-gray-900">
            {{ props.title }}
          </div>
          <div class="text-sm font-normal text-gray-500 text-center mb-3">
            {{ props.description }}
          </div>
          <div
            v-for="(starter_suggestion) in props.starterSuggestions"
            :key="starter_suggestion"
            class="border border-gray-200 text-gray-700 p-4 rounded-lg text-sm font-medium max-w-lg w-full cursor-pointer"
            @click="handleSuggestionClick(starter_suggestion)"
          >
            {{ starter_suggestion }}
          </div>
        </div>
      </template>
      <template v-else>
        <div
          class="mx-auto space-y-6"
          :class="props.variant === 'full' ? 'w-3/4' : 'w-[calc(100%-8px)]  px-4'"
        >
          <div
            v-for="(message) in messages"
            :key="message.id"
            class="flex flex-col justify-start"
            :class="[message.sender === 'user' ? 'items-end' : 'items-start']"
          >
            <NaavixThoughtsTimeline
              :function-calls="message.function_calls"
              :function-responses="message.function_responses"
            />
            <template v-for="function_response in message.function_responses" :key="function_response.id">
              <template
                v-if="
                  ['chart'].includes(function_response.response.artifact_type)
                    && render_map[function_response.response.artifact_type]
                "
              >
                <component
                  :is="render_map[function_response.response.artifact_type]"
                  :title="function_calls_map[function_response.id].title"
                  :description="function_calls_map[function_response.id].description"
                  :data="function_response.response"
                  @close="emit('close')"
                  @scroll-to-bottom="scrollToBottom"
                />
              </template>
            </template>
            <div
              v-if="message.is_streaming && !message.message && !message.current_function_call?.title"
              class="flex items-center gap-2 mb-2"
            >
              <!-- <IconHawkAiAgent /> -->
              <div class="text-sm font-normal shimmer">
                {{ $t('Thinking') }}...
              </div>
            </div>
            <div
              v-else-if="message.is_streaming && !message.message && message.current_function_call?.title"
            >
              <!-- class="flex items-center gap-2 mt-[16px] ml-4 text-sm font-semibold text-gray-900" -->
              <!-- <IconHawkAiAgent />
              NaaviX -->
            </div>
            <div
              v-else
              class="rounded-lg"
              :class="[
                message.sender === 'user'
                  ? 'bg-gray-100 text-gray-900 rounded-tr-none px-4 py-3'
                  : 'text-gray-900 rounded-tl-none',
              ]"
            >
              <!-- <div
                v-if="message.sender === 'assistant'"
                class="flex items-center gap-2 text-sm font-semibold text-gray-900 mt-1 mb-2"
              >
                <IconHawkAiAgent />
                NaaviX
              </div> -->
              <div class="prose prose-sm" v-html="renderMarkdown(message.message)" />
            </div>
            <NaavixCurrentThought
              v-if="message.current_function_call"
              :current-thought="message.current_function_call"
            />
            <NaavixArtifactsList
              :function-calls-map="function_calls_map"
              :function-responses="message.function_responses"
              :is-this-message-streaming="message.is_streaming"
            />
            <!-- <div v-if="message.function_calls?.length && !message.is_streaming" class="mt-3">
              <HawkButton type="outlined" @click="exportMessageAsReport">
                <IconHawkDownloadOne />
                Export as report
              </HawkButton>
            </div> -->
          </div>
        </div>
        <div v-if="!state.is_streaming && messages.length !== 0 && !state.usage_quota_reached" class="flex flex-col gap-1 w-[calc(100%-8px)] mx-auto px-4 mt-3">
          <!-- <div class="flex items-center gap-1 text-xs font-normal text-gray-500 mb-1">
            <IconHawkStarSix class="w-3 h-3 text-gray-600" />
            Suggestions:
          </div>
          <div
            v-for="follow_up_question_suggestion in follow_up_question_suggestions"
            :key="follow_up_question_suggestion"
            class="border border-gray-300 rounded-lg text-xs font-medium text-gray-700 px-2 py-1 cursor-pointer w-fit hover:bg-gray-50"
            @click="handleSuggestionClick(follow_up_question_suggestion)"
          >
            {{ follow_up_question_suggestion }}
          </div> -->
        </div>
        <div v-if="state.usage_quota_reached" class="mx-5 my-6 px-2 py-1 bg-error-50 border border-error-300 rounded text-xs font-medium text-error-700 flex items-center justify-between">
          <span class="flex items-start gap-1 italic">
            <IconHawkAlertTriangle class="w-4 h-4 mt-0.5" />
            {{ $t('You have reached the usage quota. Please try again after sometime.') }}
          </span>
          <span class="hover:text-error-800 cursor-pointer h-4 flex items-center gap-1" @click="sendMessage(true)">
            <IconHawkRefreshCwOne class="w-4 h-4" />
            {{ $t('Retry') }}
          </span>
        </div>
      </template>
    </div>

    <div class="border-t border-gray-200">
      <div class="relative">
        <div
          v-show="!isAtBottom && messages.length > 0"
          v-tippy="{ content: 'Scroll to the bottom', placement: 'top' }"
          class="absolute right-5 -top-14 bg-gray-900 text-white shadow-xl cursor-pointer p-2 rounded-full"
          @click="scrollToBottom(true, state.is_streaming)"
        >
          <IconHawkChevronDown class="w-5 h-5" />
        </div>
        <div
          v-if="
            actions_map?.[messages?.[messages.length - 1]?.current_function_call?.name]
              && actions_map[messages[messages.length - 1].current_function_call.name].type === 'implicit_component'
              && !state.is_streaming
          "
        >
          <component
            :is="actions_map[messages[messages.length - 1].current_function_call.name].component"
            v-show="false"
            :function-call="messages[messages.length - 1].current_function_call"
            @continue="onActionContinue"
            @cancel="onActionCancel"
          />
        </div>
        <div
          v-if="
            actions_map?.[messages?.[messages.length - 1]?.current_function_call?.name]
              && actions_map[messages[messages.length - 1].current_function_call.name].type === 'explicit'
              && !state.is_streaming
          "
          class="rounded-xl border border-primary-600 m-3"
        >
          <component
            :is="actions_map[messages[messages.length - 1].current_function_call.name].component"
            :function-call="messages[messages.length - 1].current_function_call"
            @continue="onActionContinue"
            @cancel="onActionCancel"
          />
        </div>
        <div
          v-else
          class="border border-gray-200 rounded-lg shadow-sm"
          :class="props.variant === 'full' ? 'w-3/4 mx-auto mt-3' : 'mx-3 mt-3'"
        >
          <div ref="inputContainer" class="relative flex flex-col">
            <textarea
              ref="textarea"
              v-model="state.user_input"
              rows="1"
              class="w-full resize-none rounded-xl px-4 py-3 min-h-[48px] max-h-[160px] text-sm scrollbar"
              :placeholder="$t('Ask me anything about the project')"
              @input="handleInput"
              @keydown.enter="handleEnterKeyPress"
              @keydown="handleSuggestionKeydown"
            />
            <Teleport to="body">
              <transition
                enter-active-class="transition duration-200 ease-out"
                enter-from-class="transform scale-95 opacity-0"
                enter-to-class="transform scale-100 opacity-100"
                leave-active-class="transition duration-150 ease-in"
                leave-from-class="transform scale-100 opacity-100"
                leave-to-class="transform scale-95 opacity-0"
              >
                <div
                  v-if="state.show_popup"
                  v-on-click-outside="closePopup"
                  class="fixed z-[10001] bg-white rounded-lg shadow-lg border border-gray-200 w-[250px]"
                  :class="[
                    state.popup_direction === 'top'
                      ? 'origin-bottom'
                      : state.popup_direction === 'bottom'
                        ? 'origin-top'
                        : 'origin-left',
                  ]"
                  :style="`left: ${state.popup_position.x}px; top: ${state.popup_position.y}px; height: 280px;`"
                >
                  <div class="h-full scrollbar">
                    <div class="p-2">
                      <template v-for="(item, i) in input_suggestion_items" :key="item.label">
                        <div
                          class="text-sm font-medium text-gray-900 flex items-center gap-2 p-2 hover:bg-gray-50 rounded-lg cursor-pointer mb-1"
                          :class="state.suggestion_index === getFlatIndex(i, null) ? 'bg-gray-100' : ''"
                          :data-suggestion-ref="item.label"
                          @click="insertMention(item)"
                          @mousedown.prevent
                        >
                          {{ item.label }}
                        </div>
                        <div class="text-xs text-gray-500 ml-4">
                          <div
                            v-for="(child, j) in item.children"
                            :key="child.label"
                            class="hover:bg-gray-50 rounded-lg cursor-pointer p-2 mb-1"
                            :class="state.suggestion_index === getFlatIndex(i, j) ? 'bg-gray-100' : ''"
                            :data-suggestion-ref="`${item.label}-${child.label}`"
                            @click="insertMention(item, child)"
                            @mousedown.prevent
                          >
                            {{ child.label }}
                          </div>
                        </div>
                      </template>
                    </div>
                  </div>
                </div>
              </transition>
            </Teleport>
          </div>
          <div class="flex justify-between items-center gap-2 mx-4 mb-3">
            <div class="flex items-center gap-3">
              <HawkMenu
                :items="agents_list"
                position="fixed"
                additional_trigger_classes="!ring-0"
                class="-mt-1"
                :disabled="true"
                @select="$event => state.current_agent = $event"
              >
                <template #trigger="{ open }">
                  <div
                    class="cursor-pointer flex items-center text-xs font-medium text-gray-600"
                  >
                    {{ state.current_agent.label }}
                    <IconHawkChevronUp v-if="open" class="w-4 h-4 ml-1" />
                    <IconHawkChevronDown v-else class="w-4 h-4 ml-1" />
                  </div>
                </template>
              </HawkMenu>
              <div
                class="flex items-center gap-1 cursor-pointer border rounded px-2 py-1"
                :class="state.deep_dive ? 'border-primary-500 bg-primary-50' : 'hover:bg-gray-50'"
                @click="state.deep_dive = !state.deep_dive"
              >
                <IconHawkLightbulbTwo class="w-4 h-4 inline" />
                <span class="text-xs font-medium text-gray-700">{{ $t('Deep dive') }}</span>
              </div>
            </div>
            <button
              v-if="state.is_streaming"
              v-tippy="$t('Stop')"
              class="p-2.5 bg-gray-900 text-white rounded-lg focus:outline-none"
              @click="stopStreaming"
            >
              <IconHawkStop class="w-3 h-3 bg-white rounded" />
            </button>
            <button
              v-else
              v-tippy="$t('Send')"
              class="p-2 bg-[#107CB2] text-white rounded-lg hover:bg-[#2d7191] focus:outline-none"
              :class="{ 'opacity-50 cursor-not-allowed': !state.user_input.trim() || are_messages_loading }"
              @click="sendMessage"
            >
              <IconHawkSendThree class="w-4 h-4" />
            </button>
          </div>
        </div>
        <div class="text-xs font-normal text-gray-500 flex justify-center my-1">
          {{ $t('NaaviX accelerates insights. Double-check results.') }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@import "../styles/stylesheet.scss";

.small-placeholder::placeholder {
  @apply text-sm;
  @apply text-gray-500;
}
</style>
